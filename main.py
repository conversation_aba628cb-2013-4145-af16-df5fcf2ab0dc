from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
from contextlib import asynccontextmanager
from config import config
# import sentry_sdk  # Optional monitoring service
from routes import register, validate, cache
from services.mongo_service import mongo_service
from models import HealthResponse, ErrorResponse



logging.basicConfig(
    level=config.get_log_level(),
    format=config.LOG_FORMAT
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting Face Recognition Service...")
    
    if mongo_service.connect():
        logger.info("MongoDB connection established successfully")
    else:
        logger.error("Failed to connect to MongoDB")
    
    yield
    
    logger.info("Shutting down Face Recognition Service...")
    mongo_service.disconnect()




app = FastAPI(
    title="Face Recognition Service",
    description="""
    A comprehensive Face Recognition Service API built with FastAPI and MongoDB.

    ## Features

    * **Face Registration**: Register multiple face images for a person with work unit/agency info
    * **Face Validation**: Simplified validation against ALL registered faces (no filtering required)
    * **MongoDB Storage**: Efficient storage of 128-dimensional face vectors
    * **Global Search**: Search across all embeddings regardless of work unit or agency
    * **Confidence Scoring**: Returns both similarity score and confidence percentage

    ## Technology Stack

    * FastAPI for the web framework
    * MongoDB for face vector storage
    * face_recognition library for face detection and encoding
    * 128-dimensional face vectors for high accuracy

    ## Usage

    1. **Register faces**: Upload minimum 5 images per person using `/register-face`
    2. **Validate faces**: Upload a single image for validation using `/validate-face` (searches all records)
    3. **Monitor system**: Check health and statistics using `/health` and `/validation-stats`

    ## API Changes (v2.0)

    * **Simplified Validation**: No need to provide work_unit_id or agency_id for validation
    * **Global Search**: Validation searches across all registered faces
    * **Enhanced Response**: Returns complete user info including work_unit_id and agency_id
    * **Confidence Score**: Added confidence percentage alongside similarity score
    """,
    version="2.0.0",
    contact={
        "name": "Face Recognition Service",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=config.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(
    register.router,
    tags=["Face Registration"],
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)

app.include_router(
    validate.router,
    tags=["Face Validation"],
    responses={
        400: {"model": ErrorResponse},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)

# ✅ ADDED: Cache management endpoints
app.include_router(
    cache.router,
    tags=["Cache Management"],
    responses={
        500: {"model": ErrorResponse}
    }
)


@app.get(
    "/",
    summary="Root endpoint",
    description="Welcome message and basic API information"
)
async def root():
    return {
        "message": "Welcome to Face Recognition Service API",
        "version": "1.0.0",
        "documentation": "/docs",
        "health_check": "/health",
        "endpoints": {
            "register_face": "/register-face",
            "validate_face": "/validate-face",
            "validation_stats": "/validation-stats"
        }
    }


@app.get(
    "/health",
    response_model=HealthResponse,
    summary="Health check endpoint",
    description="Check the health status of the service and database connection"
)
async def health_check():
    """Healh check endpoint"""
    try:
        db_connected = mongo_service.is_connected()
        
        if not db_connected:
            db_connected = mongo_service.connect()
        
        status = "healthy" if db_connected else "unhealthy"
        message = "Service is running normally" if db_connected else "Database connection failed"
        
        response = HealthResponse(
            status=status,
            message=message,
            database_connected=db_connected
        )
        
        if db_connected:
            return response
        else:
            return JSONResponse(
                status_code=503,
                content=response.dict()
            )
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content=HealthResponse(
                status="unhealthy",
                message=f"Health check failed: {str(e)}",
                database_connected=False
            ).dict()
        )


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            detail=f"HTTP {exc.status_code} error occurred"
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            detail="An unexpected error occurred. Please try again later."
        ).dict()
    )

# ✅ REMOVED: Sentry debug endpoint to eliminate unnecessary overhead

if __name__ == "__main__":
    import uvicorn

    if config.is_development():
        config.print_config()

    logger.info(f"Starting server on {config.HOST}:{config.PORT}")

    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.RELOAD,
        log_level=config.LOG_LEVEL.lower()
    )
