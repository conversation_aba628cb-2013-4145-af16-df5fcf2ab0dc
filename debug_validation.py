#!/usr/bin/env python3
"""
DEBUG VALIDATION SCRIPT
========================

Script untuk debug masalah "ID yang sama terus menerus" pada face validation.

Tests:
1. Test dengan berbagai gambar
2. Analisis response patterns
3. Check face encoding consistency
4. Monitor similarity scores
"""

import asyncio
import httpx
import json
import os
from pathlib import Path
import time

# Base URL untuk testing
BASE_URL = "http://localhost:8000"

class ValidationDebugger:
    """
    Debugger untuk masalah validasi face recognition
    """
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
        self.results = []
    
    async def test_single_image(self, image_path: str) -> dict:
        """Test validasi dengan satu gambar"""
        try:
            print(f"\n🔍 Testing image: {image_path}")
            
            if not os.path.exists(image_path):
                print(f"❌ Image not found: {image_path}")
                return {"error": "Image not found", "image_path": image_path}
            
            # Read image file
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # Send validation request
            files = {"image": (os.path.basename(image_path), image_data, "image/jpeg")}
            
            start_time = time.time()
            response = await self.client.post(f"{self.base_url}/validate-face", files=files)
            request_time = time.time() - start_time
            
            result = {
                "image_path": image_path,
                "image_name": os.path.basename(image_path),
                "status_code": response.status_code,
                "request_time": request_time,
                "response": None,
                "error": None
            }
            
            if response.status_code == 200:
                response_data = response.json()
                result["response"] = response_data
                
                # Extract key info
                if response_data.get("success"):
                    match_data = response_data.get("data", {})
                    result["account_id"] = match_data.get("account_id")
                    result["name"] = match_data.get("name")
                    result["similarity_score"] = match_data.get("similarity_score")
                    result["confidence"] = match_data.get("confidence")
                    
                    print(f"✅ SUCCESS:")
                    print(f"   Account ID: {result['account_id']}")
                    print(f"   Name: {result['name']}")
                    print(f"   Similarity: {result['similarity_score']:.6f}")
                    print(f"   Confidence: {result['confidence']:.6f}")
                    print(f"   Time: {request_time:.3f}s")
                else:
                    print(f"❌ FAILED: {response_data.get('message', 'Unknown error')}")
                    result["error"] = response_data.get("message")
            
            elif response.status_code == 404:
                response_data = response.json()
                result["error"] = response_data.get("detail", "No match found")
                print(f"🔍 NO MATCH: {result['error']}")
            
            else:
                result["error"] = f"HTTP {response.status_code}"
                print(f"❌ ERROR: HTTP {response.status_code}")
                try:
                    error_data = response.json()
                    result["error"] = error_data.get("detail", result["error"])
                    print(f"   Detail: {result['error']}")
                except:
                    pass
            
            return result
            
        except Exception as e:
            print(f"❌ EXCEPTION: {e}")
            return {
                "image_path": image_path,
                "error": str(e),
                "exception": True
            }
    
    async def test_multiple_images(self, image_directory: str, max_images: int = 10):
        """Test dengan multiple images dari directory"""
        print(f"\n🚀 TESTING MULTIPLE IMAGES FROM: {image_directory}")
        print("="*60)
        
        if not os.path.exists(image_directory):
            print(f"❌ Directory not found: {image_directory}")
            return
        
        # Find image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_files = []
        
        for file_path in Path(image_directory).iterdir():
            if file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))
        
        if not image_files:
            print(f"❌ No image files found in: {image_directory}")
            return
        
        # Limit number of images
        image_files = image_files[:max_images]
        print(f"Found {len(image_files)} images to test")
        
        # Test each image
        for i, image_path in enumerate(image_files, 1):
            print(f"\n--- Test {i}/{len(image_files)} ---")
            result = await self.test_single_image(image_path)
            self.results.append(result)
            
            # Small delay between requests
            await asyncio.sleep(0.5)
    
    def analyze_results(self):
        """Analyze test results for patterns"""
        print(f"\n📊 ANALYSIS OF {len(self.results)} TESTS")
        print("="*60)
        
        if not self.results:
            print("No results to analyze")
            return
        
        # Count successful vs failed
        successful = [r for r in self.results if r.get("account_id")]
        failed = [r for r in self.results if not r.get("account_id")]
        
        print(f"✅ Successful validations: {len(successful)}")
        print(f"❌ Failed validations: {len(failed)}")
        
        if successful:
            print(f"\n🎯 SUCCESSFUL MATCHES:")
            
            # Count unique account IDs
            account_ids = [r["account_id"] for r in successful]
            unique_ids = set(account_ids)
            
            print(f"   Total matches: {len(successful)}")
            print(f"   Unique account IDs: {len(unique_ids)}")
            
            # Check if always same ID
            if len(unique_ids) == 1:
                print(f"   ⚠️  WARNING: ALL MATCHES HAVE SAME ACCOUNT ID: {list(unique_ids)[0]}")
                print(f"   This indicates a problem with the similarity calculation!")
            else:
                print(f"   ✅ Multiple different IDs found - system working correctly")
            
            # Show ID frequency
            from collections import Counter
            id_counts = Counter(account_ids)
            print(f"\n   📈 Account ID frequency:")
            for account_id, count in id_counts.most_common():
                percentage = (count / len(successful)) * 100
                print(f"      {account_id}: {count} times ({percentage:.1f}%)")
            
            # Show similarity score ranges
            similarity_scores = [r["similarity_score"] for r in successful if r.get("similarity_score")]
            if similarity_scores:
                print(f"\n   📏 Similarity score range:")
                print(f"      Min: {min(similarity_scores):.6f}")
                print(f"      Max: {max(similarity_scores):.6f}")
                print(f"      Avg: {sum(similarity_scores)/len(similarity_scores):.6f}")
            
            # Show confidence ranges
            confidences = [r["confidence"] for r in successful if r.get("confidence")]
            if confidences:
                print(f"\n   🎯 Confidence range:")
                print(f"      Min: {min(confidences):.6f}")
                print(f"      Max: {max(confidences):.6f}")
                print(f"      Avg: {sum(confidences)/len(confidences):.6f}")
        
        if failed:
            print(f"\n❌ FAILED VALIDATIONS:")
            error_types = {}
            for result in failed:
                error = result.get("error", "Unknown error")
                error_types[error] = error_types.get(error, 0) + 1
            
            for error, count in error_types.items():
                print(f"   {error}: {count} times")
    
    def save_results(self, filename: str = "validation_debug_results.json"):
        """Save results to JSON file"""
        try:
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            print(f"\n💾 Results saved to: {filename}")
        except Exception as e:
            print(f"❌ Error saving results: {e}")
    
    async def run_debug_tests(self, test_images_dir: str = None):
        """Run comprehensive debug tests"""
        print("🔍 FACE VALIDATION DEBUG TESTS")
        print("="*60)
        
        # Test with specific directory if provided
        if test_images_dir and os.path.exists(test_images_dir):
            await self.test_multiple_images(test_images_dir, max_images=10)
        else:
            # Test with common test image paths
            common_paths = [
                "D:/nuca-lale-project/load-testing/test-images",
                "./test-images",
                "./images",
                "../test-images"
            ]
            
            test_dir = None
            for path in common_paths:
                if os.path.exists(path):
                    test_dir = path
                    break
            
            if test_dir:
                await self.test_multiple_images(test_dir, max_images=10)
            else:
                print("❌ No test images directory found")
                print("Please provide test images directory or place images in:")
                for path in common_paths:
                    print(f"   - {path}")
                return
        
        # Analyze results
        self.analyze_results()
        
        # Save results
        self.save_results()
        
        await self.client.aclose()


async def main():
    """Main function"""
    import sys
    
    print("Face Validation Debugger")
    print("Make sure your Face Service is running on http://localhost:8000")
    print()
    
    # Get test images directory from command line or use default
    test_dir = None
    if len(sys.argv) > 1:
        test_dir = sys.argv[1]
        print(f"Using test images directory: {test_dir}")
    else:
        print("Usage: python debug_validation.py [test_images_directory]")
        print("Will try to find test images automatically...")
    
    debugger = ValidationDebugger()
    await debugger.run_debug_tests(test_dir)


if __name__ == "__main__":
    asyncio.run(main())
