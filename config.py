"""
Configuration module for Face Recognition Service
Centralizes all environment variable handling and configuration management
"""
from urllib.parse import quote_plus

import os
import logging
from typing import List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for Face Recognition Service"""
    
    # =============================================================================
    # SERVER CONFIGURATION
    # =============================================================================
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    RELOAD: bool = os.getenv("RELOAD", "true").lower() == "true"
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = os.getenv("DEBUG", "true").lower() == "true"
    ENABLE_DOCS: bool = os.getenv("ENABLE_DOCS", "true").lower() == "true"
    
    # =============================================================================
    # MONGODB CONFIGURATION
    # =============================================================================




    
    MONGODB_CONNECTION_STRING = f"********************************************?authSource=prod_testing"
    MONGODB_DATABASE_NAME: str = "prod_testing"
    MONGODB_COLLECTION_NAME: str = "face_embeddings"
    
    
    # MongoDB connection pool settings
    MONGODB_MAX_POOL_SIZE: int = int(os.getenv("MONGODB_MAX_POOL_SIZE", "100"))
    MONGODB_MIN_POOL_SIZE: int = int(os.getenv("MONGODB_MIN_POOL_SIZE", "10"))
    MONGODB_MAX_IDLE_TIME_MS: int = int(os.getenv("MONGODB_MAX_IDLE_TIME_MS", "30000"))
    
    # =============================================================================
    # FACE RECOGNITION CONFIGURATION
    # =============================================================================
    FACE_SIMILARITY_THRESHOLD: float = float(os.getenv("FACE_SIMILARITY_THRESHOLD", "0.6"))
    MIN_REGISTRATION_IMAGES: int = int(os.getenv("MIN_REGISTRATION_IMAGES", "5"))
    MAX_IMAGE_SIZE_MB: int = int(os.getenv("MAX_IMAGE_SIZE_MB", "10"))
    
    # =============================================================================
    # LOGGING CONFIGURATION
    # =============================================================================
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO").upper()
    LOG_FORMAT: str = os.getenv(
        "LOG_FORMAT", 
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # =============================================================================
    # SECURITY CONFIGURATION
    # =============================================================================
    CORS_ORIGINS: str = os.getenv("CORS_ORIGINS", "*")
    API_KEY: str = os.getenv("API_KEY", "")
    
    # =============================================================================
    # PERFORMANCE CONFIGURATION
    # =============================================================================
    REQUEST_TIMEOUT: int = int(os.getenv("REQUEST_TIMEOUT", "30"))
    UPLOAD_TIMEOUT: int = int(os.getenv("UPLOAD_TIMEOUT", "60"))
    
    @classmethod
    def get_cors_origins(cls) -> List[str]:
        """Get CORS origins as a list"""
        if cls.CORS_ORIGINS == "*":
            return ["*"]
        return [origin.strip() for origin in cls.CORS_ORIGINS.split(",")]
    
    @classmethod
    def get_log_level(cls) -> int:
        """Get logging level as integer"""
        return getattr(logging, cls.LOG_LEVEL, logging.INFO)
    
    @classmethod
    def is_production(cls) -> bool:
        """Check if running in production environment"""
        return cls.ENVIRONMENT.lower() == "production"
    
    @classmethod
    def is_development(cls) -> bool:
        """Check if running in development environment"""
        return cls.ENVIRONMENT.lower() == "development"
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate configuration settings"""
        errors = []
        
        # Validate similarity threshold
        if not 0.0 <= cls.FACE_SIMILARITY_THRESHOLD <= 1.0:
            errors.append(f"FACE_SIMILARITY_THRESHOLD must be between 0.0 and 1.0, got {cls.FACE_SIMILARITY_THRESHOLD}")
        
        # Validate minimum registration images
        if cls.MIN_REGISTRATION_IMAGES < 1:
            errors.append(f"MIN_REGISTRATION_IMAGES must be at least 1, got {cls.MIN_REGISTRATION_IMAGES}")
        
        # Validate max image size
        if cls.MAX_IMAGE_SIZE_MB < 1:
            errors.append(f"MAX_IMAGE_SIZE_MB must be at least 1, got {cls.MAX_IMAGE_SIZE_MB}")
        
        # Validate port
        if not 1 <= cls.PORT <= 65535:
            errors.append(f"PORT must be between 1 and 65535, got {cls.PORT}")
        
        # Validate MongoDB connection string
        if not cls.MONGODB_CONNECTION_STRING:
            errors.append("MONGODB_CONNECTION_STRING cannot be empty")
        
        if errors:
            for error in errors:
                logging.error(f"Configuration error: {error}")
            return False
        
        return True
    
    @classmethod
    def print_config(cls):
        """Print current configuration (excluding sensitive data)"""
        print("=" * 60)
        print("FACE RECOGNITION SERVICE CONFIGURATION")
        print("=" * 60)
        print(f"Environment: {cls.ENVIRONMENT}")
        print(f"Debug Mode: {cls.DEBUG}")
        print(f"Host: {cls.HOST}")
        print(f"Port: {cls.PORT}")
        print(f"Database: {cls.MONGODB_DATABASE_NAME}")
        print(f"Collection: {cls.MONGODB_COLLECTION_NAME}")
        print(f"Similarity Threshold: {cls.FACE_SIMILARITY_THRESHOLD}")
        print(f"Min Registration Images: {cls.MIN_REGISTRATION_IMAGES}")
        print(f"Max Image Size: {cls.MAX_IMAGE_SIZE_MB}MB")
        print(f"Log Level: {cls.LOG_LEVEL}")
        print(f"CORS Origins: {cls.CORS_ORIGINS}")
        print("=" * 60)


# Global configuration instance
config = Config()

# Validate configuration on import
if not config.validate_config():
    raise ValueError("Invalid configuration detected. Please check your environment variables.")
