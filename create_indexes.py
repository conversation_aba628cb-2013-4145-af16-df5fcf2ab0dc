#!/usr/bin/env python3
"""
DATABASE INDEX CREATION SCRIPT
==============================

This script creates performance indexes for the Face Recognition Service.
Run this ONCE after implementing the optimizations to improve database performance.

INDEXES CREATED:
1. Compound index on (account_id, work_unit_id, agency_id)
2. Single index on account_id for faster lookups
3. Background creation to avoid blocking operations

IMPACT: 50-80% faster database queries
"""

import logging
import sys
from services.mongo_service import mongo_service
from config import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_performance_indexes():
    """
    Create database indexes for optimal performance
    """
    try:
        logger.info("Starting database index creation...")
        
        # Connect to MongoDB
        if not mongo_service.connect():
            logger.error("Failed to connect to MongoDB")
            return False
        
        logger.info("Connected to MongoDB successfully")
        
        # Create performance indexes
        success = mongo_service.create_performance_indexes()
        
        if success:
            logger.info("✅ Database indexes created successfully!")
            logger.info("Expected performance improvement: 50-80% faster queries")
            return True
        else:
            logger.error("❌ Failed to create database indexes")
            return False
            
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")
        return False
    finally:
        mongo_service.disconnect()


def verify_indexes():
    """
    Verify that indexes were created successfully
    """
    try:
        logger.info("Verifying database indexes...")
        
        if not mongo_service.connect():
            logger.error("Failed to connect to MongoDB")
            return False
        
        # Get index information
        indexes = list(mongo_service.collection.list_indexes())
        
        logger.info("Current database indexes:")
        for idx in indexes:
            logger.info(f"  - {idx['name']}: {idx.get('key', {})}")
        
        # Check for our performance indexes
        index_names = [idx['name'] for idx in indexes]
        
        expected_indexes = [
            'account_id_1_work_unit_id_1_agency_id_1',  # Compound index
            'account_id_1'  # Single index
        ]
        
        all_present = True
        for expected in expected_indexes:
            if expected in index_names:
                logger.info(f"✅ Index '{expected}' found")
            else:
                logger.warning(f"❌ Index '{expected}' not found")
                all_present = False
        
        return all_present
        
    except Exception as e:
        logger.error(f"Error verifying indexes: {e}")
        return False
    finally:
        mongo_service.disconnect()


def main():
    """
    Main function to create and verify indexes
    """
    print("=" * 60)
    print("FACE SERVICE DATABASE INDEX CREATION")
    print("=" * 60)
    print()
    
    # Show current configuration
    print("Database Configuration:")
    print(f"  Connection: {config.MONGODB_CONNECTION_STRING}")
    print(f"  Database: {config.MONGODB_DATABASE_NAME}")
    print(f"  Collection: {config.MONGODB_COLLECTION_NAME}")
    print()
    
    # Create indexes
    print("Step 1: Creating performance indexes...")
    success = create_performance_indexes()
    
    if not success:
        print("❌ Failed to create indexes. Exiting.")
        sys.exit(1)
    
    print()
    
    # Verify indexes
    print("Step 2: Verifying indexes...")
    verified = verify_indexes()
    
    print()
    print("=" * 60)
    
    if verified:
        print("✅ SUCCESS: All indexes created and verified!")
        print()
        print("NEXT STEPS:")
        print("1. Restart your Face Service application")
        print("2. Run load tests to verify performance improvement")
        print("3. Expected improvements:")
        print("   - 50-80% faster database queries")
        print("   - Reduced latency under load")
        print("   - Better concurrent request handling")
    else:
        print("⚠️  WARNING: Some indexes may not have been created properly")
        print("Please check the logs above and retry if necessary")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
