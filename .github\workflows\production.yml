name: Deploy to VPS using Docker Hub

on:
  push:
    branches:
      - main

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Decode .env from base64
        run: |
          echo "📦 Decoding .env file from secret..."
          echo "${{ secrets.ENV_BASE64 }}" | base64 -d > .env

          if [ ! -s .env ]; then
            echo "❌ ERROR: Failed to decode .env!"
            exit 1
          fi
          echo "✅ .env file decoded successfully"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: rivopelu12/face-recognition-service:prod
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            VALIDATE_ENV=true

  # deploy-server-1:
    # needs: build-and-push
    # runs-on: ubuntu-latest
    # environment: production

    # steps:
    #   - name: Deploy to server 1
    #     uses: appleboy/ssh-action@v1.0.3
    #     with:
    #       host: ${{ secrets.VPS_HOST_1 }}
    #       username: ${{ secrets.VPS_USER_1 }}
    #       key: ${{ secrets.VPS_SSH_KEY_1 }}
    #       script: |
    #         echo "${{ secrets.DOCKER_HUB_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_HUB_USERNAME }}" --password-stdin

    #         docker stop face-recognition-api || true
    #         docker rm -f face-recognition-api || true

    #         docker pull rivopelu12/face-recognition-service:prod

    #         docker run -d --name face-recognition-api \
    #           -p 8000:8000 \
    #           -e MONGODB_CONNECTION_STRING="${{ secrets.MONGODB_CONNECTION_STRING }}" \
    #           -e ENVIRONMENT=production \
    #           -e DEBUG=false \
    #           -e HOST=0.0.0.0 \
    #           -e PORT=8000 \
    #           -e MONGODB_DATABASE_NAME=face_recognition \
    #           -e MONGODB_COLLECTION_NAME=face_embeddings \
    #           -e FACE_SIMILARITY_THRESHOLD=0.6 \
    #           -e MIN_REGISTRATION_IMAGES=5 \
    #           -e MAX_IMAGE_SIZE_MB=10 \
    #           -e LOG_LEVEL=INFO \
    #           --restart unless-stopped \
    #           --memory="1g" \
    #           --cpus="0.5" \
    #           --health-cmd="curl -f http://localhost:8000/health || exit 1" \
    #           --health-interval=30s \
    #           --health-timeout=10s \
    #           --health-start-period=40s \
    #           --health-retries=3 \
    #           rivopelu12/face-recognition-service:prod

    #         docker image prune -f
    #         docker container prune -f

    #         sleep 10
    #         docker ps | grep face-recognition-api
    #         docker logs --tail=20 face-recognition-api

    #         docker exec face-recognition-api python -c "
    #         import os
    #         from pymongo import MongoClient
    #         try:
    #             client = MongoClient(os.getenv('MONGODB_CONNECTION_STRING'))
    #             client.admin.command('ping')
    #             print('✅ MongoDB connection successful!')
    #         except Exception as e:
    #             print(f'❌ MongoDB connection failed: {e}')
    #         " || echo "MongoDB connection check failed"

  deploy-server-2:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Deploy to server 2
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST_2 }}
          username: ${{ secrets.VPS_USER_2 }}
          key: ${{ secrets.VPS_SSH_KEY_2 }}
          script: |
            echo "${{ secrets.DOCKER_HUB_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_HUB_USERNAME }}" --password-stdin

            docker stop face-recognition-api || true
            docker rm -f face-recognition-api || true

            docker pull rivopelu12/face-recognition-service:prod

            docker run -d --name face-recognition-api \
              -p 8000:8000 \
              -e MONGODB_CONNECTION_STRING="${{ secrets.MONGODB_CONNECTION_STRING }}" \
              -e ENVIRONMENT=production \
              -e DEBUG=false \
              -e HOST=0.0.0.0 \
              -e PORT=8000 \
              -e MONGODB_DATABASE_NAME=face_recognition \
              -e MONGODB_COLLECTION_NAME=face_embeddings \
              -e FACE_SIMILARITY_THRESHOLD=0.6 \
              -e MIN_REGISTRATION_IMAGES=5 \
              -e MAX_IMAGE_SIZE_MB=10 \
              -e LOG_LEVEL=INFO \
              --restart unless-stopped \
              --memory="1g" \
              --cpus="0.5" \
              --health-cmd="curl -f http://localhost:8000/health || exit 1" \
              --health-interval=30s \
              --health-timeout=10s \
              --health-start-period=40s \
              --health-retries=3 \
              rivopelu12/face-recognition-service:prod

            docker image prune -f
            docker container prune -f

            sleep 10
            docker ps | grep face-recognition-api
            docker logs --tail=20 face-recognition-api

            docker exec face-recognition-api python -c "
            import os
            from pymongo import MongoClient
            try:
                client = MongoClient(os.getenv('MONGODB_CONNECTION_STRING'))
                client.admin.command('ping')
                print('✅ MongoDB connection successful!')
            except Exception as e:
                print(f'❌ MongoDB connection failed: {e}')
            " || echo "MongoDB connection check failed"
