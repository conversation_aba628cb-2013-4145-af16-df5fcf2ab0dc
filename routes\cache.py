"""
CACHE MANAGEMENT ROUTES
=======================

Endpoints untuk mengelola JSON cache system:
- GET /cache/stats - Statistik cache
- POST /cache/refresh - Refresh cache dari database
- DELETE /cache/clear - Hapus semua cache
- GET /cache/data - Ambil data dari cache (untuk testing)
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import logging
import time
from datetime import datetime

from services.cache_service import cache_service
from models import ErrorResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/cache", tags=["Cache Management"])


@router.get("/stats", 
           summary="Get Cache Statistics",
           description="Mendapatkan informasi statistik tentang cache JSON")
async def get_cache_stats() -> Dict[str, Any]:
    """
    Mendapatkan statistik lengkap tentang cache system
    
    Returns:
        - cache_exists: Apakah file cache ada
        - cache_valid: Apakah cache masih valid (belum expired)
        - cache_file: Path file cache
        - in_memory_cache: Apakah ada cache di memory
        - metadata: Metadata cache (created_at, total_embeddings, dll)
        - settings: Konfigurasi cache
    """
    try:
        logger.info("Getting cache statistics...")
        stats = cache_service.get_cache_stats()
        
        # Add current time for reference
        stats['current_time'] = datetime.now().isoformat()
        stats['server_uptime'] = time.time()
        
        logger.info("Cache statistics retrieved successfully")
        return {
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cache statistics: {str(e)}"
        )


@router.post("/refresh",
            summary="Refresh Cache from Database", 
            description="Mengambil data terbaru dari database dan update cache JSON")
async def refresh_cache() -> Dict[str, Any]:
    """
    Refresh cache dengan mengambil semua data terbaru dari database
    
    Process:
    1. Query semua face embeddings dari MongoDB
    2. Convert ke format JSON
    3. Save ke file cache (dengan/tanpa compression)
    4. Update in-memory cache
    5. Update metadata
    
    Returns:
        - success: Status operasi
        - message: Pesan hasil
        - stats: Statistik cache setelah refresh
    """
    try:
        logger.info("Starting cache refresh from database...")
        start_time = time.time()
        
        # Refresh cache from database
        success = cache_service.refresh_cache_from_database()
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to refresh cache from database"
            )
        
        # Get updated stats
        stats = cache_service.get_cache_stats()
        refresh_time = time.time() - start_time
        
        logger.info(f"Cache refresh completed successfully in {refresh_time:.2f}s")
        
        return {
            "success": True,
            "message": f"Cache refreshed successfully in {refresh_time:.2f} seconds",
            "refresh_time_seconds": refresh_time,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing cache: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to refresh cache: {str(e)}"
        )


@router.delete("/clear",
              summary="Clear All Cache",
              description="Menghapus semua file cache JSON dan in-memory cache")
async def clear_cache() -> Dict[str, Any]:
    """
    Menghapus semua cache (file dan memory)
    
    Process:
    1. Clear in-memory cache
    2. Delete cache JSON files
    3. Delete metadata files
    4. Reset cache state
    
    Returns:
        - success: Status operasi
        - message: Pesan hasil
        - files_removed: List file yang dihapus
    """
    try:
        logger.info("Starting cache clear operation...")
        
        # Get stats before clearing (to show what was removed)
        stats_before = cache_service.get_cache_stats()
        
        # Clear cache
        success = cache_service.clear_cache()
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to clear cache"
            )
        
        logger.info("Cache cleared successfully")
        
        return {
            "success": True,
            "message": "All cache cleared successfully",
            "stats_before_clear": stats_before,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear cache: {str(e)}"
        )


@router.get("/data",
           summary="Get Cached Data (Testing)",
           description="Mengambil data dari cache untuk testing (gunakan dengan hati-hati)")
async def get_cached_data(limit: int = 10) -> Dict[str, Any]:
    """
    Mengambil data dari cache untuk testing
    
    Args:
        limit: Jumlah maksimal embeddings yang dikembalikan (default: 10)
    
    Returns:
        - success: Status operasi
        - data: Sample data dari cache
        - total_count: Total jumlah embeddings di cache
        - cache_info: Informasi cache
    
    Note: Endpoint ini untuk testing saja. Jangan gunakan di production dengan limit besar.
    """
    try:
        logger.info(f"Getting cached data with limit: {limit}")
        
        # Get cached embeddings
        cached_data = cache_service.get_cached_embeddings()
        
        if cached_data is None:
            return {
                "success": False,
                "message": "No cache data available",
                "data": [],
                "total_count": 0,
                "cache_info": cache_service.get_cache_stats()
            }
        
        # Limit data for response
        limited_data = cached_data[:limit] if limit > 0 else cached_data
        
        # Remove face vectors from response to reduce size (keep only metadata)
        response_data = []
        for embedding in limited_data:
            embedding_info = {
                'account_id': embedding.get('account_id'),
                'name': embedding.get('name'),
                'work_unit_id': embedding.get('work_unit_id'),
                'agency_id': embedding.get('agency_id'),
                'faces_count': len(embedding.get('faces', []))
            }
            response_data.append(embedding_info)
        
        logger.info(f"Returning {len(response_data)} embeddings from cache")
        
        return {
            "success": True,
            "data": response_data,
            "total_count": len(cached_data),
            "returned_count": len(response_data),
            "cache_info": cache_service.get_cache_stats(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting cached data: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cached data: {str(e)}"
        )


@router.get("/health",
           summary="Cache Health Check",
           description="Health check untuk cache system")
async def cache_health_check() -> Dict[str, Any]:
    """
    Health check untuk cache system
    
    Returns:
        - healthy: Status kesehatan cache
        - cache_available: Apakah cache tersedia
        - cache_valid: Apakah cache masih valid
        - last_refresh: Waktu refresh terakhir
    """
    try:
        stats = cache_service.get_cache_stats()
        
        # Determine health status
        healthy = (
            stats.get('cache_exists', False) and 
            stats.get('cache_valid', False)
        )
        
        metadata = stats.get('metadata', {})
        
        return {
            "healthy": healthy,
            "cache_available": stats.get('cache_exists', False),
            "cache_valid": stats.get('cache_valid', False),
            "in_memory_cache": stats.get('in_memory_cache', False),
            "last_refresh": metadata.get('created_at'),
            "total_embeddings": metadata.get('total_embeddings', 0),
            "total_faces": metadata.get('total_faces', 0),
            "file_size_bytes": metadata.get('file_size_bytes', 0),
            "compression_enabled": metadata.get('compression_enabled', False),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in cache health check: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
