from fastapi import APIRouter, UploadFile, File, HTTPException
from typing import Optional
import logging
from models import (
    ValidateFaceResponse,
    ErrorResponse
)

# Configure logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ✅ SMART IMPORT: Try optimized version first, fallback to original if needed
try:
    from services.optimized_face_service import optimized_face_service as face_service_impl
    from utils.optimized_similarity import optimized_similarity_calculator as similarity_calc
    logger.info("✅ Using optimized services")
    USE_OPTIMIZED = True
except ImportError as e:
    logger.warning(f"⚠️ Optimized services not available ({e}), falling back to original")
    from services.face_service import face_service as face_service_impl
    from utils.similarity import similarity_calculator as similarity_calc
    USE_OPTIMIZED = False

from services.mongo_service import mongo_service

router = APIRouter()


# ✅ REMOVED: Problematic dependency function that caused multipart request error


@router.post(
    "/validate-face",
    response_model=ValidateFaceResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        404: {"model": ErrorResponse, "description": "No Match Found"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    },
    summary="Validate a face against all stored embeddings",
    description="""
    Validate a face by comparing it against all stored face embeddings in the database.

    Requirements:
    - Image must contain exactly 1 face
    - Supported formats: jpg, jpeg, png, bmp, tiff
    - Returns match if similarity score ≤ threshold
    - Searches across all registered faces regardless of work unit or agency

    Returns:
    - 200: Match found with complete user information
    - 404: No match found above similarity threshold
    - 400: Invalid image or multiple faces detected
    """
)
async def validate_face(
    image: UploadFile = File(..., description="Single image file for validation")
):
    """Validate a face against all stored embeddings"""

    try:
        # ✅ FIXED: Direct parameter validation instead of dependency
        # Validate image file type
        if not face_service_impl.validate_image_format(image.filename):
            raise HTTPException(
                status_code=400,
                detail=f"Image ({image.filename}) has unsupported format. "
                       f"Supported formats: {', '.join(face_service_impl.supported_formats)}"
            )

        logger.info("Starting face validation against all stored embeddings")

        # Check MongoDB connection
        if not mongo_service.is_connected():
            if not mongo_service.connect():
                raise HTTPException(
                    status_code=500,
                    detail="Database connection failed"
                )

        # ✅ OPTIMIZED: Process the uploaded image (async if available)
        if USE_OPTIMIZED:
            face_vector = await face_service_impl.process_single_image_async(image)
        else:
            face_vector = await face_service_impl.process_single_image(image)

        if not face_vector:
            raise HTTPException(
                status_code=400,
                detail="No face detected in the uploaded image or multiple faces found. "
                       "Please ensure the image contains exactly one face."
            )

        # ✅ OPTIMIZED: Query database with limit to prevent memory issues
        candidate_embeddings = mongo_service.find_embeddings_optimized(limit=100)

        if not candidate_embeddings:
            logger.info("No face embeddings found in database")
            raise HTTPException(
                status_code=404,
                detail="No registered faces found in the database"
            )

        logger.info(f"Found {len(candidate_embeddings)} candidate embeddings to compare against")

        # ✅ OPTIMIZED: Find the best match using similarity calculation
        if USE_OPTIMIZED:
            match_result = similarity_calc.find_best_match_optimized(
                target_vector=face_vector.vector,
                candidate_embeddings=candidate_embeddings,
                max_candidates=50  # Limit for faster processing
            )
        else:
            match_result = similarity_calc.find_best_match(
                target_vector=face_vector.vector,
                candidate_embeddings=candidate_embeddings
            )

        if not match_result:
            logger.info("No matching face found within similarity threshold")
            raise HTTPException(
                status_code=404,
                detail="No matching face found. The person may not be registered or the image quality may be insufficient."
            )

        best_match, similarity_score, confidence = match_result

        logger.info(f"Face validation successful for account_id: {best_match.account_id} with score: {similarity_score}, confidence: {confidence}")

        return ValidateFaceResponse(
            account_id=best_match.account_id,
            name=best_match.name,
            work_unit_id=best_match.work_unit_id,
            agency_id=best_match.agency_id,
            confidence=round(confidence, 4),
            similarity_score=round(similarity_score, 4)
        )
    
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    
    except Exception as e:
        logger.error(f"Unexpected error during face validation: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during face validation: {str(e)}"
        )


@router.get(
    "/validation-stats",
    summary="Get validation statistics",
    description="Get statistics about the face validation system"
)
async def get_validation_stats():
    """Get validation statistics"""
    try:
        # Check MongoDB connection
        if not mongo_service.is_connected():
            if not mongo_service.connect():
                raise HTTPException(
                    status_code=500,
                    detail="Database connection failed"
                )

        # Get basic statistics from MongoDB
        total_accounts = mongo_service.collection.count_documents({})

        # Get accounts with work_unit_id
        work_unit_accounts = mongo_service.collection.count_documents(
            {"work_unit_id": {"$ne": None, "$exists": True}}
        )

        # Get accounts with agency_id
        agency_accounts = mongo_service.collection.count_documents(
            {"agency_id": {"$ne": None, "$exists": True}}
        )

        # Calculate total face vectors
        pipeline = [
            {"$project": {"faces_count": {"$size": "$faces"}}},
            {"$group": {"_id": None, "total_faces": {"$sum": "$faces_count"}}}
        ]

        result = list(mongo_service.collection.aggregate(pipeline))
        total_faces = result[0]["total_faces"] if result else 0

        return {
            "total_registered_accounts": total_accounts,
            "accounts_with_work_unit": work_unit_accounts,
            "accounts_with_agency": agency_accounts,
            "total_face_vectors": total_faces,
            "similarity_threshold": similarity_calc.similarity_threshold,
            "validation_mode": "global_search",
            "search_scope": "all_embeddings",
            "database_connected": mongo_service.is_connected(),
            "api_version": "simplified"
        }
        
    except Exception as e:
        logger.error(f"Error getting validation stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving validation statistics: {str(e)}"
        )
