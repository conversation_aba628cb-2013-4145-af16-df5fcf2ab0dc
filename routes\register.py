from fastapi import APIRouter, Form, UploadFile, File, HTTPException, Depends
from typing import List, Optional
import logging
from models import (
    RegisterFaceResponse, 
    ErrorResponse, 
    FaceEmbedding, 
    FaceVector
)
from services.face_service import face_service
from services.optimized_face_service import optimized_face_service  # ✅ ADDED: Optimized version
from services.mongo_service import mongo_service
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()


async def validate_registration_data(
    account_id: str = Form(..., description="Unique account identifier"),
    name: str = Form(..., description="Person's name"),
    work_unit_id: Optional[str] = Form(None, description="Work unit identifier"),
    agency_id: Optional[str] = Form(None, description="Agency identifier"),
    images: List[UploadFile] = File(..., description="Array of image files (minimum 5)")
):
    """Validate registration form data"""
    
    # Validate required fields
    if not account_id or not account_id.strip():
        raise HTTPException(status_code=400, detail="account_id is required and cannot be empty")
    
    if not name or not name.strip():
        raise HTTPException(status_code=400, detail="name is required and cannot be empty")
    
    # Validate minimum number of images
    min_images = config.MIN_REGISTRATION_IMAGES
    if len(images) < min_images:
        raise HTTPException(
            status_code=400,
            detail=f"Minimum {min_images} images required, but only {len(images)} provided"
        )
    
    # Validate image file types
    for i, image in enumerate(images):
        if not face_service.validate_image_format(image.filename):
            raise HTTPException(
                status_code=400,
                detail=f"Image {i+1} ({image.filename}) has unsupported format. "
                       f"Supported formats: {', '.join(face_service.supported_formats)}"
            )
    
    return {
        "account_id": account_id.strip(),
        "name": name.strip(),
        "work_unit_id": work_unit_id.strip() if work_unit_id else None,
        "agency_id": agency_id.strip() if agency_id else None,
        "images": images
    }


@router.post(
    "/register-face",
    response_model=RegisterFaceResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    },
    summary="Register face embeddings for a person",
    description="""
    Register face embeddings for a person by uploading multiple images.
    
    Requirements:
    - Minimum 5 images must be provided
    - Each image must contain exactly 1 face
    - Supported formats: jpg, jpeg, png, bmp, tiff
    - If account_id already exists, new face vectors will be appended
    """
)
async def register_face(
    validated_data: dict = Depends(validate_registration_data)
):
    """Register face embeddings for a person"""
    
    try:
        account_id = validated_data["account_id"]
        name = validated_data["name"]
        work_unit_id = validated_data["work_unit_id"]
        agency_id = validated_data["agency_id"]
        images = validated_data["images"]
        
        logger.info(f"Starting face registration for account_id: {account_id}")
        
        # Check MongoDB connection
        if not mongo_service.is_connected():
            if not mongo_service.connect():
                raise HTTPException(
                    status_code=500,
                    detail="Database connection failed"
                )
        
        # ✅ OPTIMIZED: Process all uploaded images concurrently
        face_vectors = await optimized_face_service.process_multiple_images_async(images)

        # Validate minimum number of processed faces
        min_images = config.MIN_REGISTRATION_IMAGES
        if not optimized_face_service.validate_minimum_images(face_vectors, minimum=min_images):
            raise HTTPException(
                status_code=400,
                detail=f"Only {len(face_vectors)} valid faces detected. "
                       f"Minimum {min_images} faces required. Please ensure each image contains exactly one face."
            )
        
        # Check if account already exists
        existing_embedding = mongo_service.find_by_account_id(account_id)
        
        if existing_embedding:
            # Update existing account with new face vectors
            success = mongo_service.update_face_vectors(account_id, face_vectors)
            if not success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to update existing face embeddings"
                )
            
            # Get total faces count after update
            total_faces = mongo_service.get_total_faces_count(account_id)
            
            logger.info(f"Updated existing account {account_id} with {len(face_vectors)} new faces")
            
            return RegisterFaceResponse(
                message=f"Successfully updated face embeddings for existing account",
                account_id=account_id,
                faces_processed=len(face_vectors),
                total_faces_stored=total_faces
            )
        
        else:
            # Create new face embedding document
            face_embedding = FaceEmbedding(
                account_id=account_id,
                name=name,
                work_unit_id=work_unit_id,
                agency_id=agency_id,
                faces=face_vectors
            )
            
            # Insert into MongoDB
            success = mongo_service.insert_face_embedding(face_embedding)
            if not success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to store face embeddings in database"
                )
            
            logger.info(f"Created new account {account_id} with {len(face_vectors)} faces")
            
            return RegisterFaceResponse(
                message=f"Successfully registered new face embeddings",
                account_id=account_id,
                faces_processed=len(face_vectors),
                total_faces_stored=len(face_vectors)
            )
    
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    
    except Exception as e:
        logger.error(f"Unexpected error during face registration: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during face registration: {str(e)}"
        )
