#!/usr/bin/env python3
"""
CACHE SYSTEM TESTING SCRIPT
============================

Script untuk testing JSON cache system yang baru diimplementasikan.

Tests:
1. Cache refresh dari database
2. Cache stats dan health check
3. Cache data retrieval
4. Cache clear functionality
5. Performance comparison (cache vs database)
"""

import asyncio
import httpx
import time
import json
from typing import Dict, Any

# Base URL untuk testing
BASE_URL = "http://localhost:8000"

class CacheSystemTester:
    """
    Comprehensive tester untuk cache system
    """
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def test_cache_health(self) -> Dict[str, Any]:
        """Test cache health check endpoint"""
        print("\n" + "="*50)
        print("TEST 1: Cache Health Check")
        print("="*50)
        
        try:
            response = await self.client.get(f"{self.base_url}/cache/health")
            result = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Healthy: {result.get('healthy', False)}")
            print(f"Cache Available: {result.get('cache_available', False)}")
            print(f"Cache Valid: {result.get('cache_valid', False)}")
            print(f"Total Embeddings: {result.get('total_embeddings', 0)}")
            print(f"Total Faces: {result.get('total_faces', 0)}")
            print(f"File Size: {result.get('file_size_bytes', 0)} bytes")
            
            return result
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return {"error": str(e)}
    
    async def test_cache_stats(self) -> Dict[str, Any]:
        """Test cache statistics endpoint"""
        print("\n" + "="*50)
        print("TEST 2: Cache Statistics")
        print("="*50)
        
        try:
            response = await self.client.get(f"{self.base_url}/cache/stats")
            result = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Success: {result.get('success', False)}")
            
            if result.get('success'):
                data = result.get('data', {})
                print(f"Cache Exists: {data.get('cache_exists', False)}")
                print(f"Cache Valid: {data.get('cache_valid', False)}")
                print(f"In Memory Cache: {data.get('in_memory_cache', False)}")
                print(f"Cache File: {data.get('cache_file', 'None')}")
                
                metadata = data.get('metadata', {})
                if metadata:
                    print(f"Created At: {metadata.get('created_at', 'Unknown')}")
                    print(f"Total Embeddings: {metadata.get('total_embeddings', 0)}")
                    print(f"Total Faces: {metadata.get('total_faces', 0)}")
                    print(f"File Size: {metadata.get('file_size_bytes', 0)} bytes")
                    print(f"Compression: {metadata.get('compression_enabled', False)}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return {"error": str(e)}
    
    async def test_cache_refresh(self) -> Dict[str, Any]:
        """Test cache refresh from database"""
        print("\n" + "="*50)
        print("TEST 3: Cache Refresh from Database")
        print("="*50)
        
        try:
            print("Starting cache refresh...")
            start_time = time.time()
            
            response = await self.client.post(f"{self.base_url}/cache/refresh")
            result = response.json()
            
            refresh_time = time.time() - start_time
            
            print(f"Status Code: {response.status_code}")
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'No message')}")
            print(f"Refresh Time: {refresh_time:.2f}s")
            print(f"Server Refresh Time: {result.get('refresh_time_seconds', 0):.2f}s")
            
            if result.get('success'):
                stats = result.get('stats', {})
                metadata = stats.get('metadata', {})
                if metadata:
                    print(f"Total Embeddings: {metadata.get('total_embeddings', 0)}")
                    print(f"Total Faces: {metadata.get('total_faces', 0)}")
                    print(f"File Size: {metadata.get('file_size_bytes', 0)} bytes")
            
            return result
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return {"error": str(e)}
    
    async def test_cache_data_retrieval(self, limit: int = 5) -> Dict[str, Any]:
        """Test cache data retrieval"""
        print("\n" + "="*50)
        print(f"TEST 4: Cache Data Retrieval (limit={limit})")
        print("="*50)
        
        try:
            response = await self.client.get(f"{self.base_url}/cache/data?limit={limit}")
            result = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Success: {result.get('success', False)}")
            print(f"Total Count: {result.get('total_count', 0)}")
            print(f"Returned Count: {result.get('returned_count', 0)}")
            
            if result.get('success') and result.get('data'):
                print("\nSample Data:")
                for i, embedding in enumerate(result['data'][:3]):  # Show first 3
                    print(f"  {i+1}. {embedding.get('name', 'Unknown')} ({embedding.get('account_id', 'No ID')})")
                    print(f"     Work Unit: {embedding.get('work_unit_id', 'None')}")
                    print(f"     Faces: {embedding.get('faces_count', 0)}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return {"error": str(e)}
    
    async def test_performance_comparison(self) -> Dict[str, Any]:
        """Test performance comparison between cache and database"""
        print("\n" + "="*50)
        print("TEST 5: Performance Comparison")
        print("="*50)
        
        results = {}
        
        try:
            # Test cache performance (using validate endpoint)
            print("Testing cache performance...")
            cache_times = []
            
            # Create a dummy image file for testing
            dummy_image_data = b"dummy_image_content_for_testing"
            
            for i in range(3):  # Test 3 times
                start_time = time.time()
                
                # This will use cache if available
                try:
                    files = {"image": ("test.jpg", dummy_image_data, "image/jpeg")}
                    response = await self.client.post(f"{self.base_url}/validate-face", files=files)
                    # We expect this to fail (no valid image), but we measure the time to load embeddings
                except:
                    pass  # Expected to fail, we just want to measure loading time
                
                cache_time = time.time() - start_time
                cache_times.append(cache_time)
                print(f"  Cache attempt {i+1}: {cache_time:.3f}s")
            
            avg_cache_time = sum(cache_times) / len(cache_times)
            results['cache_avg_time'] = avg_cache_time
            
            print(f"Average cache time: {avg_cache_time:.3f}s")
            
            # Note: We can't easily test database-only performance without modifying the code
            # But we can show cache stats
            cache_stats = await self.test_cache_stats()
            results['cache_stats'] = cache_stats
            
            return results
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return {"error": str(e)}
    
    async def test_cache_clear(self) -> Dict[str, Any]:
        """Test cache clear functionality"""
        print("\n" + "="*50)
        print("TEST 6: Cache Clear")
        print("="*50)
        
        try:
            response = await self.client.delete(f"{self.base_url}/cache/clear")
            result = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'No message')}")
            
            # Check stats after clear
            print("\nChecking stats after clear...")
            stats_after = await self.test_cache_health()
            
            return result
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return {"error": str(e)}
    
    async def run_all_tests(self):
        """Run all cache system tests"""
        print("🚀 STARTING CACHE SYSTEM TESTS")
        print("="*60)
        
        try:
            # Test 1: Health check
            await self.test_cache_health()
            
            # Test 2: Stats
            await self.test_cache_stats()
            
            # Test 3: Refresh cache
            await self.test_cache_refresh()
            
            # Test 4: Data retrieval
            await self.test_cache_data_retrieval()
            
            # Test 5: Performance comparison
            await self.test_performance_comparison()
            
            # Test 6: Clear cache (optional - uncomment if you want to test)
            # await self.test_cache_clear()
            
            print("\n" + "="*60)
            print("✅ ALL TESTS COMPLETED")
            print("="*60)
            
        except Exception as e:
            print(f"\n❌ TEST SUITE ERROR: {e}")
        
        finally:
            await self.client.aclose()


async def main():
    """Main function to run cache system tests"""
    print("Cache System Testing Script")
    print("Make sure your Face Service is running on http://localhost:8000")
    print()
    
    tester = CacheSystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
