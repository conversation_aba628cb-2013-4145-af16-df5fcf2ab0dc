import logging
from typing import List, Optional, Dict, Any
from pymongo import Mongo<PERSON>lient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, DuplicateKeyError
from models import FaceEmbedding, FaceVector
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MongoService:
    
    def __init__(self):
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None
        self.collection: Optional[Collection] = None
        self.connection_string = config.MONGODB_CONNECTION_STRING
        self.database_name = config.MONGODB_DATABASE_NAME
        self.collection_name = config.MONGODB_COLLECTION_NAME
        
    def connect(self) -> bool:
        try:
            self.client = MongoClient(
                self.connection_string,
                maxPoolSize=config.MONGODB_MAX_POOL_SIZE,
                minPoolSize=config.MONGODB_MIN_POOL_SIZE,
                maxIdleTimeMS=config.MONGODB_MAX_IDLE_TIME_MS,
                serverSelectionTimeoutMS=config.MONGODB_SERVER_SELECTION_TIMEOUT_MS,  # ✅ Added
                socketTimeoutMS=config.MONGODB_SOCKET_TIMEOUT_MS,  # ✅ Added
                connectTimeoutMS=5000,  # ✅ Added: Fast connection timeout
                retryWrites=True,  # ✅ Added: Enable retry writes
                w='majority'  # ✅ Added: Write concern for consistency
            )
            self.client.admin.command('ping')
            self.database = self.client[self.database_name]
            self.collection = self.database[self.collection_name]

            self.collection.create_index("account_id")
            self.collection.create_index("work_unit_id")
            self.collection.create_index("agency_id")

            logger.info("Successfully connected to MongoDB")
            return True
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            return False
    
    def disconnect(self):
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    def is_connected(self) -> bool:
        try:
            if self.client:
                self.client.admin.command('ping')
                return True
        except:
            pass
        return False
    
    def insert_face_embedding(self, face_embedding: FaceEmbedding) -> bool:
        try:
            document = face_embedding.dict(by_alias=True, exclude={"id"})
            result = self.collection.insert_one(document)
            logger.info(f"Inserted face embedding with ID: {result.inserted_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting face embedding: {e}")
            return False
    
    def find_by_account_id(self, account_id: str) -> Optional[FaceEmbedding]:
        try:
            document = self.collection.find_one({"account_id": account_id})
            if document:
                return FaceEmbedding(**document)
            return None
            
        except Exception as e:
            logger.error(f"Error finding face embedding by account_id: {e}")
            return None
    
    def update_face_vectors(self, account_id: str, new_vectors: List[FaceVector]) -> bool:
        try:
            vector_dicts = [vector.dict() for vector in new_vectors]
            
            result = self.collection.update_one(
                {"account_id": account_id},
                {"$push": {"faces": {"$each": vector_dicts}}}
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated face vectors for account_id: {account_id}")
                return True
            else:
                logger.warning(f"No document updated for account_id: {account_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating face vectors: {e}")
            return False
    
    def find_by_work_unit_or_agency(self, work_unit_id: Optional[str] = None,
                                   agency_id: Optional[str] = None) -> List[FaceEmbedding]:
        try:
            query = {}
            if work_unit_id:
                query["work_unit_id"] = work_unit_id
            if agency_id:
                query["agency_id"] = agency_id

            if work_unit_id and agency_id:
                query = {"$or": [
                    {"work_unit_id": work_unit_id},
                    {"agency_id": agency_id}
                ]}

            documents = list(self.collection.find(query))
            return [FaceEmbedding(**doc) for doc in documents]

        except Exception as e:
            logger.error(f"Error finding face embeddings by work_unit/agency: {e}")
            return []

    def find_all_embeddings(self) -> List[FaceEmbedding]:
        """
        DEPRECATED: Use find_embeddings_optimized() for better performance
        This method loads ALL data and causes performance issues under load
        """
        try:
            documents = list(self.collection.find({}))
            logger.info(f"Retrieved {len(documents)} face embeddings from database")
            return [FaceEmbedding(**doc) for doc in documents]

        except Exception as e:
            logger.error(f"Error finding all face embeddings: {e}")
            return []

    def find_embeddings_optimized(self, limit: int = 100) -> List[FaceEmbedding]:
        """
        ✅ OPTIMIZED: Efficient query with limit and projection

        IMPACT: 80%+ reduction in data transfer and processing time
        - Uses limit to prevent loading excessive data
        - Projects only necessary fields
        - Much faster than find_all_embeddings()
        """
        try:
            # Only fetch necessary fields with limit
            cursor = self.collection.find(
                {},  # No filter for global search
                {
                    'account_id': 1,
                    'name': 1,
                    'work_unit_id': 1,
                    'agency_id': 1,
                    'faces': 1  # Only get face vectors
                }
            ).limit(limit)  # ✅ CRITICAL: Limit results to prevent memory issues

            documents = list(cursor)
            logger.info(f"Retrieved {len(documents)} embeddings (optimized with limit {limit})")

            return [FaceEmbedding(**doc) for doc in documents]

        except Exception as e:
            logger.error(f"Error in optimized embeddings query: {e}")
            return []

    def create_performance_indexes(self) -> bool:
        """
        ✅ CRITICAL: Create database indexes for better performance

        Run this once to improve query performance significantly
        """
        try:
            # Create compound index for common queries
            self.collection.create_index([
                ("account_id", 1),
                ("work_unit_id", 1),
                ("agency_id", 1)
            ], background=True)

            # Create index on account_id for faster lookups
            self.collection.create_index("account_id", background=True)

            logger.info("Performance indexes created successfully")
            return True

        except Exception as e:
            logger.error(f"Error creating performance indexes: {e}")
            return False
    
    def get_total_faces_count(self, account_id: str) -> int:
        try:
            document = self.collection.find_one(
                {"account_id": account_id}, 
                {"faces": 1}
            )
            if document and "faces" in document:
                return len(document["faces"])
            return 0
            
        except Exception as e:
            logger.error(f"Error getting faces count: {e}")
            return 0
    
    def delete_by_account_id(self, account_id: str) -> bool:
        try:
            result = self.collection.delete_one({"account_id": account_id})
            if result.deleted_count > 0:
                logger.info(f"Deleted face embedding for account_id: {account_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error deleting face embedding: {e}")
            return False


mongo_service = MongoService()
