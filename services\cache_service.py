"""
JSON CACHE SERVICE
==================

Service untuk caching data face embeddings ke JSON file untuk performa yang lebih baik.

Features:
- Cache semua data face embeddings ke JSON
- Auto-refresh cache dari database
- Cache invalidation dan cleanup
- Thread-safe operations
- Compression untuk file size optimization
"""

import json
import os
import time
import logging
import threading
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import gzip
import shutil
from pathlib import Path

from models import FaceEmbedding
from services.mongo_service import mongo_service
from config import config

logger = logging.getLogger(__name__)


class CacheService:
    """
    High-performance JSON cache service for face embeddings
    """
    
    def __init__(self):
        self.cache_dir = Path("cache")
        self.cache_file = self.cache_dir / "face_embeddings.json"
        self.cache_file_compressed = self.cache_dir / "face_embeddings.json.gz"
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Cache settings
        self.cache_ttl_hours = 24  # Cache valid for 24 hours
        self.use_compression = True  # Use gzip compression
        self.auto_refresh = True  # Auto refresh expired cache
        
        # In-memory cache
        self._memory_cache: Optional[List[Dict]] = None
        self._cache_loaded_at: Optional[datetime] = None
        
        # Ensure cache directory exists
        self.cache_dir.mkdir(exist_ok=True)
        
        logger.info(f"CacheService initialized - Cache dir: {self.cache_dir}")
    
    def get_cache_metadata(self) -> Dict[str, Any]:
        """Get cache metadata information"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error reading cache metadata: {e}")
            return {}
    
    def save_cache_metadata(self, metadata: Dict[str, Any]):
        """Save cache metadata"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving cache metadata: {e}")
    
    def is_cache_valid(self) -> bool:
        """Check if cache is still valid based on TTL"""
        try:
            metadata = self.get_cache_metadata()
            if not metadata.get('created_at'):
                return False
            
            created_at = datetime.fromisoformat(metadata['created_at'])
            expiry_time = created_at + timedelta(hours=self.cache_ttl_hours)
            
            is_valid = datetime.now() < expiry_time
            logger.debug(f"Cache valid: {is_valid}, Created: {created_at}, Expires: {expiry_time}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Error checking cache validity: {e}")
            return False
    
    def get_cache_file_path(self) -> Path:
        """Get the appropriate cache file path (compressed or uncompressed)"""
        if self.use_compression and self.cache_file_compressed.exists():
            return self.cache_file_compressed
        elif self.cache_file.exists():
            return self.cache_file
        return None
    
    def load_cache_from_file(self) -> Optional[List[Dict]]:
        """Load cache data from JSON file"""
        try:
            cache_path = self.get_cache_file_path()
            if not cache_path:
                logger.info("No cache file found")
                return None
            
            logger.info(f"Loading cache from: {cache_path}")
            
            if cache_path.suffix == '.gz':
                # Load compressed cache
                with gzip.open(cache_path, 'rt', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                # Load uncompressed cache
                with open(cache_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            logger.info(f"Cache loaded successfully: {len(data)} embeddings")
            return data
            
        except Exception as e:
            logger.error(f"Error loading cache from file: {e}")
            return None
    
    def save_cache_to_file(self, data: List[Dict]) -> bool:
        """Save cache data to JSON file"""
        try:
            logger.info(f"Saving cache: {len(data)} embeddings")
            
            # Save metadata
            metadata = {
                'created_at': datetime.now().isoformat(),
                'total_embeddings': len(data),
                'total_faces': sum(len(item.get('faces', [])) for item in data),
                'compression_enabled': self.use_compression,
                'file_size_bytes': 0  # Will be updated after saving
            }
            
            if self.use_compression:
                # Save compressed
                with gzip.open(self.cache_file_compressed, 'wt', encoding='utf-8') as f:
                    json.dump(data, f, separators=(',', ':'))  # Compact JSON
                
                # Update file size in metadata
                metadata['file_size_bytes'] = self.cache_file_compressed.stat().st_size
                cache_path = self.cache_file_compressed
                
                # Remove uncompressed file if exists
                if self.cache_file.exists():
                    self.cache_file.unlink()
            else:
                # Save uncompressed
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)
                
                # Update file size in metadata
                metadata['file_size_bytes'] = self.cache_file.stat().st_size
                cache_path = self.cache_file
                
                # Remove compressed file if exists
                if self.cache_file_compressed.exists():
                    self.cache_file_compressed.unlink()
            
            # Save metadata
            self.save_cache_metadata(metadata)
            
            logger.info(f"Cache saved successfully to {cache_path} ({metadata['file_size_bytes']} bytes)")
            return True
            
        except Exception as e:
            logger.error(f"Error saving cache to file: {e}")
            return False
    
    def refresh_cache_from_database(self) -> bool:
        """Refresh cache by loading all data from database"""
        try:
            with self._lock:
                logger.info("Refreshing cache from database...")
                start_time = time.time()
                
                # Get all embeddings from database
                embeddings = mongo_service.find_all_embeddings()
                
                if not embeddings:
                    logger.warning("No embeddings found in database")
                    return False
                
                # Convert to JSON-serializable format
                cache_data = []
                for embedding in embeddings:
                    embedding_dict = {
                        'account_id': embedding.account_id,
                        'name': embedding.name,
                        'work_unit_id': embedding.work_unit_id,
                        'agency_id': embedding.agency_id,
                        'faces': []
                    }
                    
                    for face in embedding.faces:
                        face_dict = {
                            'vector': face.vector
                        }
                        embedding_dict['faces'].append(face_dict)
                    
                    cache_data.append(embedding_dict)
                
                # Save to file
                success = self.save_cache_to_file(cache_data)
                
                if success:
                    # Update in-memory cache
                    self._memory_cache = cache_data
                    self._cache_loaded_at = datetime.now()
                    
                    load_time = time.time() - start_time
                    logger.info(f"Cache refreshed successfully in {load_time:.2f}s - {len(cache_data)} embeddings")
                    return True
                else:
                    logger.error("Failed to save cache to file")
                    return False
                    
        except Exception as e:
            logger.error(f"Error refreshing cache from database: {e}")
            return False
    
    def get_cached_embeddings(self) -> Optional[List[Dict]]:
        """Get cached embeddings (from memory or file)"""
        try:
            with self._lock:
                # Check if we have valid in-memory cache
                if (self._memory_cache is not None and 
                    self._cache_loaded_at is not None and
                    (datetime.now() - self._cache_loaded_at).seconds < 300):  # 5 minutes memory cache
                    logger.debug("Returning in-memory cache")
                    return self._memory_cache
                
                # Check if file cache is valid
                if not self.is_cache_valid():
                    if self.auto_refresh:
                        logger.info("Cache expired, auto-refreshing...")
                        if self.refresh_cache_from_database():
                            return self._memory_cache
                        else:
                            logger.error("Auto-refresh failed")
                            return None
                    else:
                        logger.warning("Cache expired and auto-refresh disabled")
                        return None
                
                # Load from file if not in memory
                if self._memory_cache is None:
                    logger.info("Loading cache from file...")
                    self._memory_cache = self.load_cache_from_file()
                    self._cache_loaded_at = datetime.now()
                
                return self._memory_cache
                
        except Exception as e:
            logger.error(f"Error getting cached embeddings: {e}")
            return None
    
    def clear_cache(self) -> bool:
        """Clear all cache files and in-memory cache"""
        try:
            with self._lock:
                logger.info("Clearing all cache...")
                
                # Clear in-memory cache
                self._memory_cache = None
                self._cache_loaded_at = None
                
                # Remove cache files
                files_removed = []
                
                if self.cache_file.exists():
                    self.cache_file.unlink()
                    files_removed.append(str(self.cache_file))
                
                if self.cache_file_compressed.exists():
                    self.cache_file_compressed.unlink()
                    files_removed.append(str(self.cache_file_compressed))
                
                if self.metadata_file.exists():
                    self.metadata_file.unlink()
                    files_removed.append(str(self.metadata_file))
                
                logger.info(f"Cache cleared successfully. Removed files: {files_removed}")
                return True
                
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            metadata = self.get_cache_metadata()
            cache_path = self.get_cache_file_path()
            
            stats = {
                'cache_exists': cache_path is not None,
                'cache_valid': self.is_cache_valid(),
                'cache_file': str(cache_path) if cache_path else None,
                'in_memory_cache': self._memory_cache is not None,
                'memory_cache_loaded_at': self._cache_loaded_at.isoformat() if self._cache_loaded_at else None,
                'metadata': metadata,
                'settings': {
                    'cache_ttl_hours': self.cache_ttl_hours,
                    'use_compression': self.use_compression,
                    'auto_refresh': self.auto_refresh
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}


# Global cache service instance
cache_service = CacheService()
